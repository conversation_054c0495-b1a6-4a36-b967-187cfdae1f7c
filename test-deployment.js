/**
 * <PERSON>flare Workers 部署测试脚本
 * <AUTHOR> 4.0 sonnet
 */

const WORKER_URL = 'https://cloudnav.heartwopen.workers.dev';

async function testDeployment() {
  console.log('🚀 开始测试 Cloudflare Workers 部署...\n');
  
  const tests = [
    {
      name: '主页访问测试',
      url: WORKER_URL,
      method: 'GET',
      expectedStatus: 200,
      checkContent: true,
      contentCheck: (text) => text.includes('Cloudnav') || text.includes('导航')
    },
    {
      name: '管理页面访问测试',
      url: `${WORKER_URL}/admin`,
      method: 'GET',
      expectedStatus: 200,
      checkContent: true,
      contentCheck: (text) => text.includes('管理') || text.includes('admin')
    },
    {
      name: 'API 书签列表测试',
      url: `${WORKER_URL}/api/bookmarks`,
      method: 'GET',
      expectedStatus: 200,
      checkContent: true,
      contentCheck: (text) => {
        try {
          const data = JSON.parse(text);
          return data.success !== undefined;
        } catch {
          return false;
        }
      }
    },
    {
      name: 'API 分类列表测试',
      url: `${WORKER_URL}/api/categories`,
      method: 'GET',
      expectedStatus: 200,
      checkContent: true,
      contentCheck: (text) => {
        try {
          const data = JSON.parse(text);
          return data.success !== undefined;
        } catch {
          return false;
        }
      }
    },
    {
      name: '静态资源测试',
      url: `${WORKER_URL}/favicon.ico`,
      method: 'GET',
      expectedStatus: [200, 404], // 404 也是正常的，说明路由工作
      checkContent: false
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`📋 测试: ${test.name}`);
      console.log(`   URL: ${test.url}`);
      
      const response = await fetch(test.url, {
        method: test.method,
        headers: {
          'User-Agent': 'CloudNav-Test/1.0'
        }
      });

      const status = response.status;
      const expectedStatuses = Array.isArray(test.expectedStatus) 
        ? test.expectedStatus 
        : [test.expectedStatus];

      if (!expectedStatuses.includes(status)) {
        console.log(`   ❌ 状态码错误: 期望 ${test.expectedStatus}, 实际 ${status}`);
        continue;
      }

      if (test.checkContent) {
        const text = await response.text();
        if (!test.contentCheck(text)) {
          console.log(`   ❌ 内容检查失败`);
          console.log(`   响应内容: ${text.substring(0, 200)}...`);
          continue;
        }
      }

      console.log(`   ✅ 通过 (状态码: ${status})`);
      passedTests++;

    } catch (error) {
      console.log(`   ❌ 请求失败: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('📊 测试结果汇总:');
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${Math.round(passedTests / totalTests * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！Cloudflare Workers 部署成功！');
    console.log(`🌐 访问地址: ${WORKER_URL}`);
    console.log(`🔧 管理后台: ${WORKER_URL}/admin`);
  } else {
    console.log('\n⚠️ 部分测试失败，请检查部署配置');
  }
}

// 运行测试
testDeployment().catch(console.error);
